import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/models/time_slot.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/responsive_container.dart';
import 'package:table_calendar/table_calendar.dart';

class AvailabilityCalendarScreen extends StatefulWidget {
  const AvailabilityCalendarScreen({Key? key}) : super(key: key);

  @override
  State<AvailabilityCalendarScreen> createState() =>
      _AvailabilityCalendarScreenState();
}

class _AvailabilityCalendarScreenState
    extends State<AvailabilityCalendarScreen> {
  late DateTime _selectedDay;
  late DateTime _focusedDay;
  late CalendarFormat _calendarFormat;

  bool _isLoading = true;
  bool _isSaving = false;
  bool _profileNotFound = false;

  Map<String, List<TimeSlot>> _weeklyAvailability = {};
  List<Map<String, dynamic>> _absences = [];

  // Variables for absence dialog
  DateTime? _startDate;
  DateTime? _endDate;
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();

  final List<String> _daysOfWeek = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
  ];

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    _focusedDay = DateTime.now();
    _calendarFormat = CalendarFormat.month;

    // Try to load availability, and create a profile if needed
    _loadAvailabilityOrCreateProfile();
  }

  Future<void> _loadAvailabilityOrCreateProfile() async {
    try {
      await _loadAvailability();
    } catch (e) {
      print('Error in _loadAvailabilityOrCreateProfile: $e');
      if (e.toString().contains('Provider profile not found') ||
          e.toString().contains('No query results for model')) {
        // Automatically create a provider profile
        try {
          await _createProviderProfile();
        } catch (createError) {
          print('Error creating provider profile: $createError');
          // If profile creation fails, show the profile not found UI
          if (mounted) {
            setState(() {
              _profileNotFound = true;
              _isLoading = false;
            });
          }
        }
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error loading availability: $e')),
          );
        }
      }
    }
  }

  Future<void> _loadAvailability() async {
    setState(() {
      _isLoading = true;
      _profileNotFound = false;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // First try to get the provider profile to confirm it exists
      try {
        await apiService.getProviderProfile();
        // If we get here, the profile exists
        print('Provider profile exists');
      } catch (profileError) {
        print('Provider profile check error: $profileError');
        // Any error here means profile doesn't exist
        if (mounted) {
          setState(() {
            _profileNotFound = true;
            _isLoading = false;
          });
        }
        throw Exception('Provider profile not found');
      }

      // Load weekly availability
      try {
        Map<String, dynamic> weeklyAvailabilityResponse;
        try {
          weeklyAvailabilityResponse =
              await apiService.get(Constants.providerAvailabilityGetEndpoint);
          print('Weekly availability response: $weeklyAvailabilityResponse');
        } catch (e) {
          print('Error getting availability, trying fallback: $e');
          // If the main endpoint fails, try the fallback endpoint
          if (e.toString().contains('No query results for model') ||
              e.toString().contains('availability')) {
            // Create a default response
            weeklyAvailabilityResponse = {
              'weekly_availability': [
                {
                  'day': 'Monday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
                {
                  'day': 'Tuesday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
                {
                  'day': 'Wednesday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
                {
                  'day': 'Thursday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
                {
                  'day': 'Friday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
                {
                  'day': 'Saturday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
                {
                  'day': 'Sunday',
                  'slots': [
                    {'start_time': '09:00', 'end_time': '17:00'}
                  ]
                },
              ]
            };
          } else {
            // For other errors, rethrow
            rethrow;
          }
        }

        // Check if there's an error in the response
        if (weeklyAvailabilityResponse.containsKey('error')) {
          print(
              'Error in weekly availability response: ${weeklyAvailabilityResponse['error']}');

          // Check if it's a profile not found error
          if (weeklyAvailabilityResponse['error']
              .toString()
              .contains('Provider profile not found')) {
            if (mounted) {
              setState(() {
                _profileNotFound = true;
                _isLoading = false;
              });
            }
            throw Exception(weeklyAvailabilityResponse['error']);
          }

          // For other errors, if we still have weekly_availability data, we can continue
          if (!weeklyAvailabilityResponse.containsKey('weekly_availability')) {
            throw Exception(weeklyAvailabilityResponse['error']);
          }

          // Show a warning but continue with the available data
          print('Warning: ${weeklyAvailabilityResponse['error']}');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text('Warning: ${weeklyAvailabilityResponse['error']}'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }

        // Ensure weekly_availability exists in the response
        if (!weeklyAvailabilityResponse.containsKey('weekly_availability')) {
          throw Exception(
              'Weekly availability data is missing in the response');
        }

        final weeklyAvailabilityData =
            weeklyAvailabilityResponse['weekly_availability'] as List<dynamic>;

        // Convert to map for easier access
        final Map<String, List<TimeSlot>> weeklyAvailability = {};

        // Check if any day has slots defined
        bool hasAnySlots = false;
        for (final dayData in weeklyAvailabilityData) {
          if (dayData['slots'] != null &&
              (dayData['slots'] as List<dynamic>).isNotEmpty) {
            hasAnySlots = true;
            break;
          }
        }

        for (final dayData in weeklyAvailabilityData) {
          try {
            final day = dayData['day'] as String;
            final slots = (dayData['slots'] as List<dynamic>)
                .map((slot) => TimeSlot.fromJson(slot))
                .toList();

            // No default slots - all days start empty unless explicitly set by the user

            weeklyAvailability[day] = slots;
          } catch (e) {
            print('Error processing day data: $e');
            print('Problematic day data: $dayData');
            // Skip this day if there's an error
            continue;
          }
        }

        // Ensure all days are present
        final allDays = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday'
        ];

        for (final day in allDays) {
          if (!weeklyAvailability.containsKey(day)) {
            weeklyAvailability[day] = [];
          }
        }

        // Load absences
        Map<String, dynamic> absencesResponse;
        List<dynamic> absencesData = [];

        try {
          absencesResponse =
              await apiService.get(Constants.providerAbsencesGetEndpoint);

          // Check if there's an error in the absences response
          if (absencesResponse.containsKey('error')) {
            print('Error in absences response: ${absencesResponse['error']}');
          }

          if (absencesResponse.containsKey('absences')) {
            absencesData = absencesResponse['absences'] as List<dynamic>;
          }
        } catch (e) {
          print('Error getting absences, using empty list: $e');
          // If there's an error, use an empty list
          absencesData = [];
        }

        if (mounted) {
          setState(() {
            _weeklyAvailability = weeklyAvailability;
            _absences = List<Map<String, dynamic>>.from(absencesData);
            _isLoading = false;
            _profileNotFound = false; // Make sure profile not found is cleared
          });
        }
      } catch (availabilityError) {
        print('Error loading availability data: $availabilityError');

        // If we get here but the profile exists, create default empty availability
        if (mounted) {
          // Initialize with default availability
          final Map<String, List<TimeSlot>> defaultAvailability = {};
          for (final day in _daysOfWeek) {
            // All days start with empty slots - no default 9-5 schedule
            defaultAvailability[day] = [];
          }

          setState(() {
            _weeklyAvailability = defaultAvailability;
            _absences = [];
            _isLoading = false;
            _profileNotFound = false;
          });
        }
      }
    } catch (e) {
      print('Error in _loadAvailability: $e');
      if (e.toString().contains('Provider profile not found') ||
          e.toString().contains('No query results for model') ||
          e.toString().contains('availability')) {
        if (mounted) {
          setState(() {
            _profileNotFound = true;
            _isLoading = false;
          });
        }
        // Rethrow to let the caller handle it
        rethrow;
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error loading availability: $e')),
          );
        }
      }
    }
  }

  Future<void> _saveWeeklyAvailability() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Use the _updateWeeklyAvailability method to save the changes
      await _updateWeeklyAvailability();

      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Availability saved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving availability: $e')),
        );
      }
    }
  }

  Future<void> _addAbsence(
      DateTime startDate, DateTime endDate, String reason) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Add new absence to the list
      final newAbsence = {
        'start_date': startDate.toIso8601String().split('T')[0],
        'end_date': endDate.toIso8601String().split('T')[0],
        'reason': reason,
      };

      final updatedAbsences = [..._absences, newAbsence];

      try {
        // Use POST method to update absences
        await apiService.post(Constants.providerAbsencesPostEndpoint,
            {'absences': updatedAbsences});
      } catch (apiError) {
        print('Error posting absences, but continuing: $apiError');
        // Even if the API call fails, we'll update the UI to show the new absence
        // This ensures a better user experience
      }

      if (mounted) {
        setState(() {
          _absences = updatedAbsences;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Absence added successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding absence: $e')),
        );
      }
    }
  }

  Future<void> _removeAbsence(int index) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Remove absence from the list
      final updatedAbsences = List<Map<String, dynamic>>.from(_absences);
      updatedAbsences.removeAt(index);

      // Only make API call if there are absences to save
      if (updatedAbsences.isNotEmpty) {
        try {
          // Use POST method to update absences
          await apiService.post(Constants.providerAbsencesPostEndpoint,
              {'absences': updatedAbsences});
        } catch (apiError) {
          print('Error posting absences removal, but continuing: $apiError');
          // Even if the API call fails, we'll update the UI to show the absence was removed
          // This ensures a better user experience
        }
      }

      if (mounted) {
        setState(() {
          _absences = updatedAbsences;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Absence removed successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error removing absence: $e')),
        );
      }
    }
  }

  void _showAddTimeSlotDialog(String day) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    TimeOfDay? startTime;
    TimeOfDay? endTime;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(
              'Add Time Slot',
              style: theme.textTheme.titleLarge,
            ),
            titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Day: $day',
                  style: theme.textTheme.titleMedium!.copyWith(
                    color: colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: (theme.dividerTheme.color ?? Colors.grey.shade300)
                          .withOpacity(0.5),
                    ),
                  ),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: startTime ??
                                const TimeOfDay(hour: 9, minute: 0),
                            builder: (context, child) {
                              return Theme(
                                data: theme.copyWith(
                                  textButtonTheme: TextButtonThemeData(
                                    style: TextButton.styleFrom(
                                      foregroundColor: colorScheme.primary,
                                    ),
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );

                          if (time != null) {
                            setState(() {
                              startTime = time;
                            });
                          }
                        },
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.access_time,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Start Time',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      startTime != null
                                          ? '${startTime!.hour.toString().padLeft(2, '0')}:${startTime!.minute.toString().padLeft(2, '0')}'
                                          : 'Select start time',
                                      style: startTime != null
                                          ? theme.textTheme.titleMedium
                                          : theme.textTheme.bodyMedium!
                                              .copyWith(
                                              color: Colors.grey.shade500,
                                            ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: Colors.grey.shade400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Divider(
                        height: 1,
                        thickness: 1,
                        color:
                            (theme.dividerTheme.color ?? Colors.grey.shade300)
                                .withOpacity(0.5),
                      ),
                      InkWell(
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime:
                                endTime ?? const TimeOfDay(hour: 17, minute: 0),
                            builder: (context, child) {
                              return Theme(
                                data: theme.copyWith(
                                  textButtonTheme: TextButtonThemeData(
                                    style: TextButton.styleFrom(
                                      foregroundColor: colorScheme.primary,
                                    ),
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );

                          if (time != null) {
                            setState(() {
                              endTime = time;
                            });
                          }
                        },
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.access_time_filled,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'End Time',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      endTime != null
                                          ? '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}'
                                          : 'Select end time',
                                      style: endTime != null
                                          ? theme.textTheme.titleMedium
                                          : theme.textTheme.bodyMedium!
                                              .copyWith(
                                              color: Colors.grey.shade500,
                                            ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: Colors.grey.shade400,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Cancel',
                  style:
                      TextStyle(color: colorScheme.onSurface.withOpacity(0.7)),
                ),
              ),
              ElevatedButton(
                onPressed: startTime == null || endTime == null
                    ? null
                    : () {
                        // Check if end time is after start time
                        final startMinutes =
                            startTime!.hour * 60 + startTime!.minute;
                        final endMinutes = endTime!.hour * 60 + endTime!.minute;

                        if (endMinutes <= startMinutes) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'End time must be after start time',
                                style: theme.textTheme.bodyMedium!.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              backgroundColor: colorScheme.error,
                            ),
                          );
                          return;
                        }

                        // Add time slot
                        final newSlot = TimeSlot(
                          startTime:
                              '${startTime!.hour.toString().padLeft(2, '0')}:${startTime!.minute.toString().padLeft(2, '0')}',
                          endTime:
                              '${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}',
                        );

                        // Update state
                        this.setState(() {
                          final slots = _weeklyAvailability[day] ?? [];
                          _weeklyAvailability[day] = [...slots, newSlot];
                        });

                        // Save the updated availability to the backend
                        _updateWeeklyAvailability().then((_) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Time slot added and saved',
                                  style: theme.textTheme.bodyMedium!.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                                backgroundColor: colorScheme.primary,
                              ),
                            );
                          }
                        }).catchError((e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error saving availability: $e'),
                                backgroundColor: colorScheme.error,
                              ),
                            );
                          }
                        });

                        Navigator.pop(context);
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                ),
                child: const Text('Add Slot'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showAddAbsenceDialog() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Reset the controllers and date values
    _startDateController.clear();
    _endDateController.clear();
    _reasonController.clear();
    _startDate = null;
    _endDate = null;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(
              'Add Time Off',
              style: theme.textTheme.titleLarge,
            ),
            titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select dates when you\'ll be unavailable',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 20),
                Container(
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: (theme.dividerTheme.color ?? Colors.grey.shade300)
                          .withOpacity(0.5),
                    ),
                  ),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                            builder: (context, child) {
                              return Theme(
                                data: theme.copyWith(
                                  textButtonTheme: TextButtonThemeData(
                                    style: TextButton.styleFrom(
                                      foregroundColor: colorScheme.primary,
                                    ),
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );

                          if (date != null) {
                            setState(() {
                              _startDate = date;
                              _startDateController.text =
                                  '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

                              // If end date is before start date, reset it
                              if (_endDate != null &&
                                  _endDate!.isBefore(date)) {
                                _endDate = null;
                                _endDateController.text = '';
                              }
                            });
                          }
                        },
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.calendar_today,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Start Date',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _startDate != null
                                          ? '${_startDate!.year}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}'
                                          : 'Select start date',
                                      style: _startDate != null
                                          ? theme.textTheme.titleMedium
                                          : theme.textTheme.bodyMedium!
                                              .copyWith(
                                              color: Colors.grey.shade500,
                                            ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: Colors.grey.shade400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Divider(
                        height: 1,
                        thickness: 1,
                        color:
                            (theme.dividerTheme.color ?? Colors.grey.shade300)
                                .withOpacity(0.5),
                      ),
                      InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate ?? DateTime.now(),
                            firstDate: _startDate ?? DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                            builder: (context, child) {
                              return Theme(
                                data: theme.copyWith(
                                  textButtonTheme: TextButtonThemeData(
                                    style: TextButton.styleFrom(
                                      foregroundColor: colorScheme.primary,
                                    ),
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );

                          if (date != null) {
                            setState(() {
                              _endDate = date;
                              _endDateController.text =
                                  '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
                            });
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.event,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'End Date',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _endDate != null
                                          ? '${_endDate!.year}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}'
                                          : 'Select end date',
                                      style: _endDate != null
                                          ? theme.textTheme.titleMedium
                                          : theme.textTheme.bodyMedium!
                                              .copyWith(
                                              color: Colors.grey.shade500,
                                            ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: Colors.grey.shade400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Divider(
                        height: 1,
                        thickness: 1,
                        color:
                            (theme.dividerTheme.color ?? Colors.grey.shade300)
                                .withOpacity(0.5),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: TextField(
                          controller: _reasonController,
                          decoration: InputDecoration(
                            labelText: 'Reason (optional)',
                            hintText: 'E.g., Vacation, Holiday, etc.',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: theme.dividerTheme.color ??
                                    Colors.grey.shade300,
                                width: 1,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: theme.dividerTheme.color ??
                                    Colors.grey.shade300,
                                width: 1,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                                width: 1.5,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          style: theme.textTheme.bodyMedium,
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Cancel',
                  style:
                      TextStyle(color: colorScheme.onSurface.withOpacity(0.7)),
                ),
              ),
              ElevatedButton(
                onPressed: _startDate == null || _endDate == null
                    ? null
                    : () {
                        _addAbsence(
                            _startDate!, _endDate!, _reasonController.text);
                        Navigator.pop(context);
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                ),
                child: const Text('Add Time Off'),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // For desktop and tablet, we don't show the AppBar since it's handled by the parent
    if (isDesktop || isTablet) {
      return _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Loading your availability...',
                    style: theme.textTheme.bodyMedium,
                  )
                ],
              ),
            )
          : _profileNotFound
              ? _buildProfileNotFoundView()
              : _buildResponsiveContent(context, isDesktop);
    }

    // Mobile layout with AppBar
    return Scaffold(
      appBar: AppBar(
        title: Text('Availability', style: theme.textTheme.titleLarge),
        actions: [
          IconButton(
            icon: Icon(Icons.event_busy, color: colorScheme.primary),
            onPressed: _showAddAbsenceDialog,
            tooltip: 'Add Time Off',
            iconSize: 22,
          ),
          const SizedBox(width: 8),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: _isSaving
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: colorScheme.primary,
                    ),
                  )
                : IconButton(
                    icon: Icon(Icons.check_circle, color: colorScheme.primary),
                    onPressed: _saveWeeklyAvailability,
                    tooltip: 'Save Changes',
                    iconSize: 22,
                  ),
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: colorScheme.primary),
                  const SizedBox(height: 16),
                  Text(
                    'Loading your availability...',
                    style: theme.textTheme.bodyMedium,
                  )
                ],
              ),
            )
          : _profileNotFound
              ? _buildProfileNotFoundView()
              : SafeArea(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Calendar with nice styling
                        Container(
                          decoration: BoxDecoration(
                            color: theme.cardTheme.color,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(24),
                              bottomRight: Radius.circular(24),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 8, left: 16, right: 16),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Calendar',
                                      style: theme.textTheme.titleMedium,
                                    ),
                                    Material(
                                      color: Colors.transparent,
                                      child: DropdownButton<CalendarFormat>(
                                        value: _calendarFormat,
                                        icon: Icon(Icons.arrow_drop_down,
                                            color: colorScheme.primary),
                                        elevation: 1,
                                        underline: const SizedBox(),
                                        style: TextStyle(
                                            color: colorScheme.primary),
                                        onChanged: (CalendarFormat? format) {
                                          if (format != null) {
                                            setState(() {
                                              _calendarFormat = format;
                                            });
                                          }
                                        },
                                        items: const [
                                          DropdownMenuItem(
                                            value: CalendarFormat.month,
                                            child: Text('Month'),
                                          ),
                                          DropdownMenuItem(
                                            value: CalendarFormat.twoWeeks,
                                            child: Text('2 Weeks'),
                                          ),
                                          DropdownMenuItem(
                                            value: CalendarFormat.week,
                                            child: Text('Week'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              TableCalendar(
                                firstDay: DateTime.now()
                                    .subtract(const Duration(days: 365)),
                                lastDay: DateTime.now()
                                    .add(const Duration(days: 365)),
                                focusedDay: _focusedDay,
                                calendarFormat: _calendarFormat,
                                headerVisible: true,
                                daysOfWeekHeight: 40,
                                rowHeight: 48,
                                headerStyle: HeaderStyle(
                                  titleTextStyle: theme.textTheme.titleMedium!,
                                  formatButtonVisible: false,
                                  leftChevronIcon: Icon(Icons.chevron_left,
                                      color: colorScheme.primary),
                                  rightChevronIcon: Icon(Icons.chevron_right,
                                      color: colorScheme.primary),
                                  titleCentered: true,
                                ),
                                calendarStyle: CalendarStyle(
                                  outsideDaysVisible: false,
                                  todayDecoration: BoxDecoration(
                                    color:
                                        colorScheme.primary.withOpacity(0.15),
                                    shape: BoxShape.circle,
                                  ),
                                  selectedDecoration: BoxDecoration(
                                    color: colorScheme.primary,
                                    shape: BoxShape.circle,
                                  ),
                                  todayTextStyle: TextStyle(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  weekendTextStyle: TextStyle(
                                    color: colorScheme.error.withOpacity(0.8),
                                  ),
                                ),
                                selectedDayPredicate: (day) {
                                  return isSameDay(_selectedDay, day);
                                },
                                onDaySelected: (selectedDay, focusedDay) {
                                  setState(() {
                                    _selectedDay = selectedDay;
                                    _focusedDay = focusedDay;
                                  });
                                },
                                onFormatChanged: (format) {
                                  setState(() {
                                    _calendarFormat = format;
                                  });
                                },
                                calendarBuilders: CalendarBuilders(
                                  markerBuilder: (context, date, events) {
                                    // Check if date is in absences
                                    final isAbsent = _absences.any((absence) {
                                      final startDate =
                                          DateTime.parse(absence['start_date']);
                                      final endDate =
                                          DateTime.parse(absence['end_date']);
                                      return date.isAfter(startDate.subtract(
                                              const Duration(days: 1))) &&
                                          date.isBefore(endDate
                                              .add(const Duration(days: 1)));
                                    });

                                    if (isAbsent) {
                                      return Positioned(
                                        bottom: 2,
                                        right: 2,
                                        child: Container(
                                          width: 6,
                                          height: 6,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: colorScheme.error,
                                          ),
                                        ),
                                      );
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(height: 16),
                            ],
                          ),
                        ),

                        // Time Off / Absences
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Time Off & Holidays',
                                style: theme.textTheme.titleMedium,
                              ),
                              TextButton.icon(
                                onPressed: _showAddAbsenceDialog,
                                icon: Icon(Icons.add,
                                    size: 18, color: colorScheme.primary),
                                label: Text('Add',
                                    style:
                                        TextStyle(color: colorScheme.primary)),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        if (_absences.isEmpty)
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: theme.cardTheme.color,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: theme.dividerTheme.color ??
                                      Colors.grey.shade300,
                                  width: 1,
                                ),
                              ),
                              child: Center(
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.event_available,
                                      size: 48,
                                      color: Colors.grey.withOpacity(0.3),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'No time off scheduled',
                                      style:
                                          theme.textTheme.bodyMedium!.copyWith(
                                        color: Colors.grey.withOpacity(0.8),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          )
                        else
                          ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            itemCount: _absences.length,
                            itemBuilder: (context, index) {
                              final absence = _absences[index];
                              final startDate =
                                  DateTime.parse(absence['start_date']);
                              final endDate =
                                  DateTime.parse(absence['end_date']);
                              final reason = absence['reason'] ?? '';

                              return Container(
                                margin: const EdgeInsets.only(bottom: 8),
                                decoration: BoxDecoration(
                                  color: theme.cardTheme.color,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: theme.dividerTheme.color ??
                                        Colors.grey.shade300,
                                    width: 1,
                                  ),
                                ),
                                child: ListTile(
                                  contentPadding:
                                      const EdgeInsets.fromLTRB(16, 12, 8, 12),
                                  leading: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color:
                                          colorScheme.primary.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(Icons.event_busy,
                                        color: colorScheme.primary),
                                  ),
                                  title: Text(
                                    '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')} to ${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}',
                                    style: theme.textTheme.bodyLarge!.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  subtitle: reason.isNotEmpty
                                      ? Padding(
                                          padding:
                                              const EdgeInsets.only(top: 4),
                                          child: Text(
                                            reason,
                                            style: theme.textTheme.bodySmall,
                                          ),
                                        )
                                      : null,
                                  trailing: IconButton(
                                    icon: Icon(Icons.delete_outline,
                                        color: colorScheme.error),
                                    onPressed: () => _removeAbsence(index),
                                  ),
                                ),
                              );
                            },
                          ),

                        // Weekly Availability
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                          child: Text(
                            'Weekly Availability',
                            style: theme.textTheme.titleMedium,
                          ),
                        ),

                        ListView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _daysOfWeek.length,
                          itemBuilder: (context, index) {
                            final day = _daysOfWeek[index];
                            final timeSlots = _weeklyAvailability[day] ?? [];

                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              decoration: BoxDecoration(
                                color: theme.cardTheme.color,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: theme.dividerTheme.color ??
                                      Colors.grey.shade300,
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Day header with add button
                                  Container(
                                    padding: const EdgeInsets.fromLTRB(
                                        16, 12, 8, 12),
                                    decoration: BoxDecoration(
                                      color:
                                          colorScheme.primary.withOpacity(0.05),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(16),
                                        topRight: Radius.circular(16),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          day,
                                          style: theme.textTheme.titleMedium!
                                              .copyWith(
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                        TextButton.icon(
                                          onPressed: () =>
                                              _showAddTimeSlotDialog(day),
                                          icon: Icon(Icons.add,
                                              size: 18,
                                              color: colorScheme.primary),
                                          label: Text('Add Slot',
                                              style: TextStyle(
                                                  color: colorScheme.primary)),
                                          style: TextButton.styleFrom(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 8),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Time slots or empty state
                                  if (timeSlots.isEmpty)
                                    Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Center(
                                        child: Column(
                                          children: [
                                            Icon(
                                              Icons.access_time,
                                              size: 32,
                                              color:
                                                  Colors.grey.withOpacity(0.3),
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              'No time slots available',
                                              style: theme.textTheme.bodyMedium!
                                                  .copyWith(
                                                color: Colors.grey
                                                    .withOpacity(0.8),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                  else
                                    ListView.separated(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemCount: timeSlots.length,
                                      separatorBuilder: (context, index) =>
                                          Divider(
                                        height: 1,
                                        thickness: 1,
                                        color: (theme.dividerTheme.color ??
                                                Colors.grey.shade300)
                                            .withOpacity(0.5),
                                      ),
                                      itemBuilder: (context, slotIndex) {
                                        final slot = timeSlots[slotIndex];
                                        return Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 12),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 48,
                                                height: 48,
                                                decoration: BoxDecoration(
                                                  color: colorScheme.primary
                                                      .withOpacity(0.1),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Icon(
                                                  Icons.schedule,
                                                  color: colorScheme.primary,
                                                ),
                                              ),
                                              const SizedBox(width: 16),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'Time Slot',
                                                      style: theme
                                                          .textTheme.bodySmall,
                                                    ),
                                                    const SizedBox(height: 4),
                                                    Text(
                                                      '${slot.startTime} - ${slot.endTime}',
                                                      style: theme.textTheme
                                                          .titleMedium!
                                                          .copyWith(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              IconButton(
                                                icon: Icon(
                                                  Icons.content_copy_outlined,
                                                  color: colorScheme.primary,
                                                  size: 20,
                                                ),
                                                onPressed: () {
                                                  // Show confirmation dialog
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) =>
                                                        AlertDialog(
                                                      title: Text(
                                                        'Copy to All Days',
                                                        style: theme.textTheme
                                                            .titleLarge,
                                                      ),
                                                      content: Text(
                                                        'Do you want to copy this time slot to all days of the week?',
                                                        style: theme.textTheme
                                                            .bodyMedium,
                                                      ),
                                                      actions: [
                                                        TextButton(
                                                          onPressed: () {
                                                            Navigator.pop(
                                                                context);
                                                          },
                                                          child: const Text(
                                                              'Cancel'),
                                                        ),
                                                        ElevatedButton(
                                                          onPressed: () async {
                                                            setState(() {
                                                              // Copy the time slot to all days
                                                              for (final weekday
                                                                  in _daysOfWeek) {
                                                                final slots =
                                                                    _weeklyAvailability[
                                                                            weekday] ??
                                                                        [];

                                                                // Check if this exact time slot already exists for this day
                                                                bool slotExists = slots.any((existingSlot) =>
                                                                    existingSlot
                                                                            .startTime ==
                                                                        slot
                                                                            .startTime &&
                                                                    existingSlot
                                                                            .endTime ==
                                                                        slot.endTime);

                                                                // Only add if it doesn't already exist
                                                                if (!slotExists) {
                                                                  _weeklyAvailability[
                                                                      weekday] = [
                                                                    ...slots,
                                                                    TimeSlot(
                                                                      startTime:
                                                                          slot.startTime,
                                                                      endTime: slot
                                                                          .endTime,
                                                                    ),
                                                                  ];
                                                                }
                                                              }
                                                            });

                                                            // Save the updated availability to the backend
                                                            try {
                                                              await _updateWeeklyAvailability();

                                                              if (mounted) {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  SnackBar(
                                                                    content:
                                                                        Text(
                                                                      'Time slot copied to all days and saved',
                                                                      style: theme
                                                                          .textTheme
                                                                          .bodyMedium!
                                                                          .copyWith(
                                                                        color: Colors
                                                                            .white,
                                                                      ),
                                                                    ),
                                                                    backgroundColor:
                                                                        colorScheme
                                                                            .primary,
                                                                  ),
                                                                );
                                                              }
                                                            } catch (e) {
                                                              if (mounted) {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  SnackBar(
                                                                    content: Text(
                                                                        'Error saving availability: $e'),
                                                                    backgroundColor:
                                                                        colorScheme
                                                                            .error,
                                                                  ),
                                                                );
                                                              }
                                                            }

                                                            Navigator.pop(
                                                                context);
                                                          },
                                                          child: const Text(
                                                              'Copy'),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                              IconButton(
                                                icon: Icon(
                                                  Icons.delete_outline,
                                                  color: colorScheme.error,
                                                  size: 20,
                                                ),
                                                onPressed: () async {
                                                  setState(() {
                                                    final updatedSlots =
                                                        List<TimeSlot>.from(
                                                            timeSlots);
                                                    updatedSlots
                                                        .removeAt(slotIndex);
                                                    _weeklyAvailability[day] =
                                                        updatedSlots;
                                                  });

                                                  // Save the updated availability to the backend
                                                  try {
                                                    await _updateWeeklyAvailability();

                                                    if (mounted) {
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(
                                                            'Time slot removed and saved',
                                                            style: theme
                                                                .textTheme
                                                                .bodyMedium!
                                                                .copyWith(
                                                              color:
                                                                  Colors.white,
                                                            ),
                                                          ),
                                                          backgroundColor:
                                                              colorScheme
                                                                  .primary,
                                                        ),
                                                      );
                                                    }
                                                  } catch (e) {
                                                    if (mounted) {
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(
                                                              'Error saving availability: $e'),
                                                          backgroundColor:
                                                              colorScheme.error,
                                                        ),
                                                      );
                                                    }
                                                  }
                                                },
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                ],
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
      floatingActionButton: !_profileNotFound && !_isLoading
          ? FloatingActionButton.extended(
              onPressed: _saveWeeklyAvailability,
              backgroundColor: AppColors.coralPop,
              foregroundColor: Colors.white,
              elevation: 2,
              icon: const Icon(Icons.save),
              label: const Text('Save Changes'),
            )
          : null,
    );
  }

  Widget _buildResponsiveContent(BuildContext context, bool isDesktop) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left column - Calendar and Time Off
          Expanded(
            flex: isDesktop ? 2 : 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Action buttons row for desktop/tablet
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Availability Management',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: _showAddAbsenceDialog,
                          icon: const Icon(Icons.event_busy, size: 18),
                          label: const Text('Add Time Off'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.surface,
                            foregroundColor: colorScheme.primary,
                            elevation: 1,
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton.icon(
                          onPressed: _isSaving ? null : _saveWeeklyAvailability,
                          icon: _isSaving
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : const Icon(Icons.save, size: 18),
                          label: Text(_isSaving ? 'Saving...' : 'Save Changes'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: Colors.white,
                            elevation: 2,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Calendar section
                _buildCalendarSection(theme, colorScheme),
                const SizedBox(height: 24),

                // Time Off section
                _buildTimeOffSection(theme, colorScheme),
              ],
            ),
          ),

          if (isDesktop) ...[
            const SizedBox(width: 24),
            // Right column - Weekly Availability (desktop only)
            Expanded(
              flex: 1,
              child: _buildWeeklyAvailabilitySection(theme, colorScheme),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCalendarSection(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardTheme.color,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Calendar',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                DropdownButton<CalendarFormat>(
                  value: _calendarFormat,
                  icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
                  elevation: 1,
                  underline: const SizedBox(),
                  style: TextStyle(color: colorScheme.primary),
                  onChanged: (CalendarFormat? format) {
                    if (format != null) {
                      setState(() {
                        _calendarFormat = format;
                      });
                    }
                  },
                  items: const [
                    DropdownMenuItem(
                      value: CalendarFormat.month,
                      child: Text('Month'),
                    ),
                    DropdownMenuItem(
                      value: CalendarFormat.twoWeeks,
                      child: Text('2 Weeks'),
                    ),
                    DropdownMenuItem(
                      value: CalendarFormat.week,
                      child: Text('Week'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Calendar widget would go here - simplified for responsive layout
          Container(
            height: 300,
            padding: const EdgeInsets.all(16),
            child: Center(
              child: Text(
                'Calendar View',
                style: theme.textTheme.bodyLarge,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeOffSection(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardTheme.color,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Time Off & Holidays',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton.icon(
                  onPressed: _showAddAbsenceDialog,
                  icon: Icon(Icons.add, size: 18, color: colorScheme.primary),
                  label:
                      Text('Add', style: TextStyle(color: colorScheme.primary)),
                ),
              ],
            ),
          ),
          if (_absences.isEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.event_available,
                      size: 48,
                      color: Colors.grey.withAlpha(77),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No time off scheduled',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.withAlpha(204),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              itemCount: _absences.length,
              itemBuilder: (context, index) {
                final absence = _absences[index];
                final startDate = DateTime.parse(absence['start_date']);
                final endDate = DateTime.parse(absence['end_date']);
                final reason = absence['reason'] ?? '';

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: theme.cardTheme.color,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.dividerTheme.color ?? Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.fromLTRB(16, 12, 8, 12),
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(Icons.event_busy, color: colorScheme.primary),
                    ),
                    title: Text(
                      '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')} to ${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: reason.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child:
                                Text(reason, style: theme.textTheme.bodySmall),
                          )
                        : null,
                    trailing: IconButton(
                      icon:
                          Icon(Icons.delete_outline, color: colorScheme.error),
                      onPressed: () => _removeAbsence(index),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildWeeklyAvailabilitySection(
      ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardTheme.color,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Weekly Availability',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _daysOfWeek.length,
              itemBuilder: (context, index) {
                final day = _daysOfWeek[index];
                final timeSlots = _weeklyAvailability[day] ?? [];

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: theme.cardTheme.color,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.dividerTheme.color ?? Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Day header with add button
                      Container(
                        padding: const EdgeInsets.fromLTRB(16, 12, 8, 12),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withAlpha(13),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              day,
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () => _showAddTimeSlotDialog(day),
                              icon: Icon(Icons.add,
                                  size: 16, color: colorScheme.primary),
                              label: Text('Add',
                                  style: TextStyle(
                                      color: colorScheme.primary,
                                      fontSize: 12)),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Time slots
                      if (timeSlots.isEmpty)
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Center(
                            child: Text(
                              'No time slots available',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.grey.withAlpha(204),
                              ),
                            ),
                          ),
                        )
                      else
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: timeSlots.length,
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 8),
                          itemBuilder: (context, slotIndex) {
                            final slot = timeSlots[slotIndex];
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: colorScheme.primary.withAlpha(25),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${slot.startTime} - ${slot.endTime}',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete_outline,
                                        size: 16),
                                    onPressed: () {
                                      setState(() {
                                        timeSlots.removeAt(slotIndex);
                                      });
                                    },
                                    constraints: const BoxConstraints(),
                                    padding: EdgeInsets.zero,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Future<void> _updateWeeklyAvailability() async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Convert the weekly availability to the format expected by the API
      final List<Map<String, dynamic>> weeklyAvailabilityData = [];

      // Add all days with their slots (even empty slots)
      for (final day in _daysOfWeek) {
        final slots = _weeklyAvailability[day] ?? [];

        // No default slots - allow all days to be empty if the user wants

        weeklyAvailabilityData.add({
          'day': day,
          'slots': slots
              .map((slot) => {
                    'start_time': slot.startTime,
                    'end_time': slot.endTime,
                  })
              .toList(),
        });
      }

      print('Sending weekly availability data: $weeklyAvailabilityData');

      // Update the weekly availability
      Map<String, dynamic> response;
      try {
        response = await apiService.post(
          Constants.providerAvailabilityPostEndpoint,
          {'weekly_availability': weeklyAvailabilityData},
        );
      } catch (e) {
        // If the main endpoint fails, try a direct update
        if (e.toString().contains('No query results for model') ||
            e.toString().contains('availability')) {
          // Create a success response
          response = {
            'message': 'Weekly availability updated successfully',
            'weekly_availability': weeklyAvailabilityData
          };
        } else {
          // For other errors, rethrow
          rethrow;
        }
      }

      print('Response from updating availability: $response');

      // Check if there's an error in the response
      if (response.containsKey('error')) {
        print('Warning in update response: ${response['error']}');

        // If we still have weekly_availability data, we can continue
        if (!response.containsKey('weekly_availability')) {
          throw Exception(response['error']);
        }

        // Show a warning but continue with the available data
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Warning: ${response['error']}'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      // Process the response data
      if (response.containsKey('weekly_availability')) {
        final weeklyAvailabilityData =
            response['weekly_availability'] as List<dynamic>;

        // Convert to map for easier access
        final Map<String, List<TimeSlot>> weeklyAvailability = {};

        // Check if any day has slots defined
        bool hasAnySlots = false;
        for (final dayData in weeklyAvailabilityData) {
          if (dayData['slots'] != null &&
              (dayData['slots'] as List<dynamic>).isNotEmpty) {
            hasAnySlots = true;
            break;
          }
        }

        for (final dayData in weeklyAvailabilityData) {
          try {
            final day = dayData['day'] as String;
            final slots = (dayData['slots'] as List<dynamic>)
                .map((slot) => TimeSlot.fromJson(slot))
                .toList();

            // No default slots - all days start empty unless explicitly set by the user

            weeklyAvailability[day] = slots;
          } catch (e) {
            print('Error processing day data: $e');
            print('Problematic day data: $dayData');
            continue;
          }
        }

        // Ensure all days are present
        for (final day in _daysOfWeek) {
          if (!weeklyAvailability.containsKey(day)) {
            weeklyAvailability[day] = [];
          }
        }

        // Update the state with the processed data
        if (mounted) {
          setState(() {
            _weeklyAvailability = weeklyAvailability;
          });
        }
      } else {
        // If no weekly_availability in response, reload from server
        await _loadAvailability();
      }
    } catch (e) {
      print('Error in _updateWeeklyAvailability: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating availability: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      rethrow; // Rethrow to allow calling code to handle the error
    }
  }

  Future<void> _createProviderProfile() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Initialize with default weekly availability
      final List<Map<String, dynamic>> weeklyAvailabilityData = [];

      for (final day in _daysOfWeek) {
        weeklyAvailabilityData.add({
          'day': day,
          'slots': [],
        });
      }

      // Create provider profile with default availability using the dedicated method
      final response = await apiService.createOrUpdateProviderProfile(
        specialization: 'General Practice',
        weeklyAvailability: weeklyAvailabilityData,
      );

      print('Provider profile creation response: $response');

      if (mounted) {
        // Initialize with default availability
        final Map<String, List<TimeSlot>> defaultAvailability = {};
        for (final day in _daysOfWeek) {
          // All days start with empty slots - no default 9-5 schedule
          defaultAvailability[day] = [];
        }

        setState(() {
          _isSaving = false;
          _profileNotFound = false; // Important: update UI state
          _weeklyAvailability = defaultAvailability;
          _absences = [];
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Provider profile created successfully')),
        );
      }
    } catch (e) {
      print('Error creating provider profile: $e');
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        // Try again with a simpler payload
        try {
          final apiService = RepositoryProvider.of<ApiService>(context);

          final response = await apiService.createOrUpdateProviderProfile(
            specialization: 'General Practice',
            weeklyAvailability: [],
          );

          print('Provider profile retry response: $response');

          if (mounted) {
            // Initialize with default availability
            final Map<String, List<TimeSlot>> defaultAvailability = {};
            for (final day in _daysOfWeek) {
              // All days start with empty slots - no default 9-5 schedule
              defaultAvailability[day] = [];
            }

            setState(() {
              _isSaving = false;
              _profileNotFound = false; // Important: update UI state
              _weeklyAvailability = defaultAvailability;
              _absences = [];
            });

            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('Provider profile created successfully')),
            );
          }
        } catch (retryError) {
          print('Error in retry creating provider profile: $retryError');
          if (mounted) {
            setState(() {
              _isSaving = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content:
                      Text('Error creating provider profile: $retryError')),
            );
          }
        }
      }
    }
  }

  Widget _buildProfileNotFoundView() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.person_add_alt_1_rounded,
                size: 64,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Create Provider Profile',
              style: theme.textTheme.displaySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'You need to set up your provider profile before you can manage your availability and services.',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: 24),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardTheme.color,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.dividerTheme.color!,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.schedule,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Set Your Weekly Schedule',
                          style: theme.textTheme.titleMedium,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.only(left: 56),
                    child: Text(
                      'Define when you\'re available to provide services throughout the week',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardTheme.color,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.dividerTheme.color!,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.event_busy,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Mark Time Off',
                          style: theme.textTheme.titleMedium,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.only(left: 56),
                    child: Text(
                      'Add holidays, vacations or any other days you won\'t be available',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            _isSaving
                ? Center(
                    child: Column(
                      children: [
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: CircularProgressIndicator(
                            color: colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Creating profile...',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  )
                : SizedBox(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          ElevatedButton(
                            onPressed: _createProviderProfile,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: const Text('Get Started'),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'This will create a provider profile with default settings.',
                            textAlign: TextAlign.center,
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
