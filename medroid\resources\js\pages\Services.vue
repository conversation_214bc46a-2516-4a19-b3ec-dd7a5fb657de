<template>
  <AppLayout title="Services Management">
    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
          <div class="p-6 lg:p-8 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
              <h1 class="text-2xl font-medium text-gray-900">Services Management</h1>
              <button
                @click="showCreateModal = true"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Add Service
              </button>
            </div>

            <!-- Filters -->
            <div class="mb-6 flex flex-wrap gap-4">
              <div class="flex-1 min-w-64">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search services..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <select
                  v-model="selectedCategory"
                  class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Categories</option>
                  <option v-for="category in categories" :key="category" :value="category">
                    {{ category }}
                  </option>
                </select>
              </div>
              <div>
                <select
                  v-model="selectedProvider"
                  class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Providers</option>
                  <option v-for="provider in providers" :key="provider.id" :value="provider.id">
                    {{ provider.user?.name || 'Unknown Provider' }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Services Table -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Provider
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="service in filteredServices" :key="service.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ service.name }}</div>
                        <div class="text-sm text-gray-500">{{ service.description }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ service.provider?.user?.name || 'Unknown' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ service.category || 'Uncategorized' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${{ service.price }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ service.duration }} min
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        :class="[
                          'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                          service.active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        ]"
                      >
                        {{ service.active ? 'Active' : 'Inactive' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        @click="editService(service)"
                        class="text-indigo-600 hover:text-indigo-900 mr-3"
                      >
                        Edit
                      </button>
                      <button
                        @click="deleteService(service)"
                        class="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Empty State -->
            <div v-if="filteredServices.length === 0" class="text-center py-12">
              <div class="text-gray-500">No services found.</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <ServiceModal
      v-if="showCreateModal || showEditModal"
      :service="selectedService"
      :providers="providers"
      @close="closeModal"
      @saved="handleServiceSaved"
    />
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';
import ServiceModal from '@/components/ServiceModal.vue';

const services = ref([]);
const providers = ref([]);
const categories = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedCategory = ref('');
const selectedProvider = ref('');
const showCreateModal = ref(false);
const showEditModal = ref(false);
const selectedService = ref(null);

const filteredServices = computed(() => {
  let filtered = services.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(service =>
      service.name.toLowerCase().includes(query) ||
      service.description?.toLowerCase().includes(query)
    );
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(service => service.category === selectedCategory.value);
  }

  if (selectedProvider.value) {
    filtered = filtered.filter(service => service.provider_id == selectedProvider.value);
  }

  return filtered;
});

const loadServices = async () => {
  loading.value = true;
  try {
    const response = await window.axios.get('/services-list');
    services.value = response.data;
  } catch (error) {
    console.error('Error loading services:', error);
  } finally {
    loading.value = false;
  }
};

const loadProviders = async () => {
  try {
    const response = await window.axios.get('/get-providers');
    providers.value = response.data;
  } catch (error) {
    console.error('Error loading providers:', error);
  }
};

const loadCategories = async () => {
  try {
    const response = await window.axios.get('/get-service-categories');
    categories.value = response.data.categories || [];
  } catch (error) {
    console.error('Error loading categories:', error);
  }
};

const editService = (service) => {
  selectedService.value = service;
  showEditModal.value = true;
};

const deleteService = async (service) => {
  if (confirm(`Are you sure you want to delete "${service.name}"?`)) {
    try {
      await window.axios.delete(`/delete-service/${service.id}`);
      await loadServices();
    } catch (error) {
      console.error('Error deleting service:', error);
      alert('Error deleting service');
    }
  }
};

const closeModal = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  selectedService.value = null;
};

const handleServiceSaved = () => {
  closeModal();
  loadServices();
  loadCategories();
};

onMounted(() => {
  loadServices();
  loadProviders();
  loadCategories();
});
</script>
