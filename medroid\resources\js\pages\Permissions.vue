<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Permissions', href: '/permissions' },
];

const loading = ref(false);
const roles = ref([]);
const permissions = ref([]);

const fetchData = async () => {
    loading.value = true;
    try {
        const [rolesResponse, permissionsResponse] = await Promise.all([
            window.axios.get('/roles-list'),
            window.axios.get('/permissions-list')
        ]);

        roles.value = rolesResponse.data;
        permissions.value = permissionsResponse.data;
    } catch (error) {
        console.error('Error fetching data:', error);
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchData();
});
</script>

<template>
    <Head title="Permissions Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Permissions Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Roles Section -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 text-gray-900 dark:text-gray-100">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium">Roles</h3>
                                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                    Add Role
                                </button>
                            </div>
                            
                            <div v-if="loading" class="text-center py-4">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                            </div>
                            
                            <div v-else class="space-y-3">
                                <div v-for="role in roles" :key="role.id" 
                                    class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <div>
                                        <h4 class="font-medium">{{ role.display_name }}</h4>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ role.permissions_count }} permissions
                                        </p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">Edit</button>
                                        <button v-if="role.name !== 'admin'" class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions Section -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 text-gray-900 dark:text-gray-100">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium">Permissions</h3>
                                <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                                    Add Permission
                                </button>
                            </div>
                            
                            <div v-if="loading" class="text-center py-4">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                            </div>
                            
                            <div v-else class="space-y-3">
                                <div v-for="permission in permissions" :key="permission.id" 
                                    class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                                    <div>
                                        <h4 class="font-medium">{{ permission.name }}</h4>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            Group: {{ permission.group }}
                                        </p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">Edit</button>
                                        <button class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role-Permission Matrix -->
                <div class="mt-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h3 class="text-lg font-medium mb-4">Role-Permission Matrix</h3>
                        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-cogs text-4xl mb-4"></i>
                            <p>Role-Permission matrix will be implemented here</p>
                            <p class="text-sm">This will allow you to assign permissions to roles</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
