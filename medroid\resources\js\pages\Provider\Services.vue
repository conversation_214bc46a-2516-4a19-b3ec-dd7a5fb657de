<template>
  <AppLayout title="My Services">
    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
          <div class="p-6 lg:p-8 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
              <h1 class="text-2xl font-medium text-gray-900">My Services</h1>
              <button
                @click="showCreateModal = true"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Add New Service
              </button>
            </div>

            <!-- Services Grid -->
            <div v-if="services.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="service in services"
                :key="service.id"
                class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <div class="p-6">
                  <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ service.name }}</h3>
                    <span
                      :class="[
                        'px-2 py-1 text-xs font-semibold rounded-full',
                        service.active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ service.active ? 'Active' : 'Inactive' }}
                    </span>
                  </div>

                  <p class="text-gray-600 text-sm mb-4">{{ service.description }}</p>

                  <div class="space-y-2 mb-4">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-500">Category:</span>
                      <span class="font-medium">{{ service.category || 'Uncategorized' }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-500">Price:</span>
                      <span class="font-medium text-green-600">${{ service.price }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-500">Duration:</span>
                      <span class="font-medium">{{ service.duration }} minutes</span>
                    </div>
                  </div>

                  <!-- Telemedicine Features -->
                  <div v-if="service.is_telemedicine" class="mb-4">
                    <div class="text-xs text-gray-500 mb-2">Telemedicine Features:</div>
                    <div class="flex flex-wrap gap-1">
                      <span v-if="service.supports_video" class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        Video
                      </span>
                      <span v-if="service.supports_audio" class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                        Audio
                      </span>
                      <span v-if="service.supports_chat" class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                        Chat
                      </span>
                    </div>
                  </div>

                  <!-- Discount Info -->
                  <div v-if="service.discount_percentage" class="mb-4 p-2 bg-yellow-50 rounded">
                    <div class="text-xs text-yellow-800">
                      {{ service.discount_percentage }}% discount
                      <span v-if="service.discount_valid_until">
                        until {{ formatDate(service.discount_valid_until) }}
                      </span>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                    <button
                      @click="editService(service)"
                      class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Edit
                    </button>
                    <button
                      @click="toggleServiceStatus(service)"
                      :class="[
                        'text-sm font-medium',
                        service.active
                          ? 'text-red-600 hover:text-red-800'
                          : 'text-green-600 hover:text-green-800'
                      ]"
                    >
                      {{ service.active ? 'Deactivate' : 'Activate' }}
                    </button>
                    <button
                      @click="deleteService(service)"
                      class="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-12">
              <div class="text-gray-500 mb-4">You haven't created any services yet.</div>
              <button
                @click="showCreateModal = true"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Create Your First Service
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <ProviderServiceModal
      v-if="showCreateModal || showEditModal"
      :service="selectedService"
      @close="closeModal"
      @saved="handleServiceSaved"
    />
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';
import ProviderServiceModal from '@/components/ProviderServiceModal.vue';

const services = ref([]);
const loading = ref(false);
const showCreateModal = ref(false);
const showEditModal = ref(false);
const selectedService = ref(null);

const loadServices = async () => {
  loading.value = true;
  try {
    const response = await window.axios.get('/services-list');
    services.value = response.data;
  } catch (error) {
    console.error('Error loading services:', error);
  } finally {
    loading.value = false;
  }
};

const editService = (service) => {
  selectedService.value = service;
  showEditModal.value = true;
};

const toggleServiceStatus = async (service) => {
  try {
    await window.axios.put(`/save-service/${service.id}`, {
      ...service,
      active: !service.active
    });
    await loadServices();
  } catch (error) {
    console.error('Error updating service status:', error);
    alert('Error updating service status');
  }
};

const deleteService = async (service) => {
  if (confirm(`Are you sure you want to delete "${service.name}"?`)) {
    try {
      await window.axios.delete(`/delete-service/${service.id}`);
      await loadServices();
    } catch (error) {
      console.error('Error deleting service:', error);
      alert('Error deleting service');
    }
  }
};

const closeModal = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  selectedService.value = null;
};

const handleServiceSaved = () => {
  closeModal();
  loadServices();
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

onMounted(() => {
  loadServices();
});
</script>
