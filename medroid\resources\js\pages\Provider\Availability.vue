<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Availability Management', href: '/provider/availability' },
];

// Reactive data
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const successMessage = ref('');

// Days of the week
const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

// Weekly availability data
const weeklyAvailability = ref({
    Monday: [],
    Tuesday: [],
    Wednesday: [],
    Thursday: [],
    Friday: [],
    Saturday: [],
    Sunday: []
});

// Absences/time-off data
const absences = ref([]);

// Form data for new time slot
const newTimeSlot = ref({
    day: 'Monday',
    startTime: '09:00',
    endTime: '17:00'
});

// Form data for new absence
const newAbsence = ref({
    startDate: '',
    endDate: '',
    reason: ''
});

// Modal states
const showTimeSlotModal = ref(false);
const showAbsenceModal = ref(false);

// Methods
const fetchAvailabilityData = async () => {
    loading.value = true;
    error.value = null;
    
    try {
        const [availabilityResponse, absencesResponse] = await Promise.all([
            axios.get('/provider/get-availability'),
            axios.get('/provider/get-absences')
        ]);
        
        // Process weekly availability
        if (availabilityResponse.data.weekly_availability) {
            const availability = {};
            daysOfWeek.forEach(day => {
                availability[day] = [];
            });
            
            availabilityResponse.data.weekly_availability.forEach(dayData => {
                if (dayData.day && dayData.slots) {
                    availability[dayData.day] = dayData.slots;
                }
            });
            
            weeklyAvailability.value = availability;
        }
        
        // Process absences
        if (absencesResponse.data.absences) {
            absences.value = absencesResponse.data.absences;
        }
        
    } catch (err) {
        console.error('Error fetching availability data:', err);
        error.value = 'Failed to load availability data. Please try again.';
    } finally {
        loading.value = false;
    }
};

const addTimeSlot = () => {
    const day = newTimeSlot.value.day;
    const slot = {
        start_time: newTimeSlot.value.startTime,
        end_time: newTimeSlot.value.endTime
    };
    
    // Validate time slot
    if (slot.start_time >= slot.end_time) {
        error.value = 'End time must be after start time';
        return;
    }
    
    // Check for overlapping slots
    const existingSlots = weeklyAvailability.value[day];
    const hasOverlap = existingSlots.some(existing => {
        return (slot.start_time < existing.end_time && slot.end_time > existing.start_time);
    });
    
    if (hasOverlap) {
        error.value = 'Time slot overlaps with existing slot';
        return;
    }
    
    weeklyAvailability.value[day].push(slot);
    weeklyAvailability.value[day].sort((a, b) => a.start_time.localeCompare(b.start_time));
    
    // Reset form
    newTimeSlot.value = {
        day: 'Monday',
        startTime: '09:00',
        endTime: '17:00'
    };
    showTimeSlotModal.value = false;
    error.value = null;
};

const removeTimeSlot = (day, index) => {
    weeklyAvailability.value[day].splice(index, 1);
};

const addAbsence = () => {
    if (!newAbsence.value.startDate || !newAbsence.value.endDate) {
        error.value = 'Please select start and end dates';
        return;
    }
    
    if (newAbsence.value.startDate > newAbsence.value.endDate) {
        error.value = 'End date must be after start date';
        return;
    }
    
    absences.value.push({
        start_date: newAbsence.value.startDate,
        end_date: newAbsence.value.endDate,
        reason: newAbsence.value.reason || 'Personal time off'
    });
    
    // Reset form
    newAbsence.value = {
        startDate: '',
        endDate: '',
        reason: ''
    };
    showAbsenceModal.value = false;
    error.value = null;
};

const removeAbsence = (index) => {
    absences.value.splice(index, 1);
};

const saveAvailability = async () => {
    saving.value = true;
    error.value = null;
    successMessage.value = '';

    try {
        // Get CSRF token from meta tag
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        console.log('CSRF Token:', token ? 'Present' : 'Missing');

        // Prepare weekly availability data
        const weeklyAvailabilityData = Object.keys(weeklyAvailability.value).map(day => ({
            day,
            slots: weeklyAvailability.value[day]
        }));

        console.log('Saving availability data:', weeklyAvailabilityData);

        // Configure request headers
        const config = {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            withCredentials: true
        };

        // Save weekly availability
        const availabilityResponse = await axios.post('/provider/save-availability', {
            weekly_availability: weeklyAvailabilityData
        }, config);

        console.log('Availability saved successfully:', availabilityResponse.data);

        // Save absences only if there are absences to save
        if (absences.value && absences.value.length > 0) {
            const absencesResponse = await axios.post('/provider/absences', {
                absences: absences.value
            }, config);

            console.log('Absences saved successfully:', absencesResponse.data);
        } else {
            console.log('No absences to save, skipping absences API call');
        }

        successMessage.value = 'Availability updated successfully!';
        setTimeout(() => {
            successMessage.value = '';
        }, 3000);

    } catch (err) {
        console.error('Error saving availability:', err);

        // Detailed error handling
        if (err.response) {
            console.error('Response status:', err.response.status);
            console.error('Response data:', err.response.data);
            console.error('Response headers:', err.response.headers);

            if (err.response.status === 401) {
                error.value = 'Authentication failed. Please refresh the page and try again.';
            } else if (err.response.status === 403) {
                error.value = 'Access denied. You may not have the required permissions.';
            } else if (err.response.status === 419) {
                error.value = 'Session expired. Please refresh the page and try again.';
            } else {
                error.value = `Failed to save availability: ${err.response.data.message || err.message}`;
            }
        } else if (err.request) {
            console.error('Request made but no response:', err.request);
            error.value = 'No response from server. Please check your connection.';
        } else {
            console.error('Error setting up request:', err.message);
            error.value = `Request error: ${err.message}`;
        }
    } finally {
        saving.value = false;
    }
};

const formatTime = (time) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

// Initialize on mount
onMounted(() => {
    fetchAvailabilityData();
});
</script>

<template>
    <Head title="Availability Management" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Availability Management</h1>
                <p class="mt-2 text-gray-600">Manage your working hours and time off</p>
            </div>

            <!-- Error/Success Messages -->
            <div v-if="error" class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                    <p class="text-red-700">{{ error }}</p>
                </div>
            </div>

            <div v-if="successMessage" class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                    <p class="text-green-700">{{ successMessage }}</p>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="text-center py-12">
                <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">Loading availability data...</p>
            </div>

            <!-- Main Content -->
            <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Weekly Availability -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold text-gray-900">Weekly Availability</h2>
                            <button
                                @click="showTimeSlotModal = true"
                                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <i class="fas fa-plus mr-2"></i>
                                Add Time Slot
                            </button>
                        </div>

                        <div class="space-y-4">
                            <div v-for="day in daysOfWeek" :key="day" class="border rounded-lg p-4">
                                <h3 class="font-medium text-gray-900 mb-3">{{ day }}</h3>
                                
                                <div v-if="weeklyAvailability[day].length === 0" class="text-gray-500 text-sm">
                                    No availability set for this day
                                </div>
                                
                                <div v-else class="space-y-2">
                                    <div
                                        v-for="(slot, index) in weeklyAvailability[day]"
                                        :key="index"
                                        class="flex justify-between items-center bg-gray-50 rounded p-3"
                                    >
                                        <span class="text-sm">
                                            {{ formatTime(slot.start_time) }} - {{ formatTime(slot.end_time) }}
                                        </span>
                                        <button
                                            @click="removeTimeSlot(day, index)"
                                            class="text-red-600 hover:text-red-800"
                                        >
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Time Off & Actions -->
                <div class="space-y-6">
                    <!-- Time Off Section -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold text-gray-900">Time Off</h2>
                            <button
                                @click="showAbsenceModal = true"
                                class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                            >
                                <i class="fas fa-plus mr-1"></i>
                                Add
                            </button>
                        </div>

                        <div v-if="absences.length === 0" class="text-gray-500 text-sm">
                            No time off scheduled
                        </div>
                        
                        <div v-else class="space-y-3">
                            <div
                                v-for="(absence, index) in absences"
                                :key="index"
                                class="border rounded p-3"
                            >
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="text-sm font-medium">{{ formatDate(absence.start_date) }} - {{ formatDate(absence.end_date) }}</p>
                                        <p class="text-xs text-gray-600 mt-1">{{ absence.reason }}</p>
                                    </div>
                                    <button
                                        @click="removeAbsence(index)"
                                        class="text-red-600 hover:text-red-800"
                                    >
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <button
                        @click="saveAvailability"
                        :disabled="saving"
                        class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                    >
                        <i v-if="saving" class="fas fa-spinner fa-spin mr-2"></i>
                        <i v-else class="fas fa-save mr-2"></i>
                        {{ saving ? 'Saving...' : 'Save Changes' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Time Slot Modal -->
        <div v-if="showTimeSlotModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <h3 class="text-lg font-semibold mb-4">Add Time Slot</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Day</label>
                        <select v-model="newTimeSlot.day" class="w-full border rounded-lg px-3 py-2">
                            <option v-for="day in daysOfWeek" :key="day" :value="day">{{ day }}</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                        <input
                            v-model="newTimeSlot.startTime"
                            type="time"
                            class="w-full border rounded-lg px-3 py-2"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                        <input
                            v-model="newTimeSlot.endTime"
                            type="time"
                            class="w-full border rounded-lg px-3 py-2"
                        />
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button
                        @click="showTimeSlotModal = false"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                        Cancel
                    </button>
                    <button
                        @click="addTimeSlot"
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                        Add Slot
                    </button>
                </div>
            </div>
        </div>

        <!-- Absence Modal -->
        <div v-if="showAbsenceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <h3 class="text-lg font-semibold mb-4">Add Time Off</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input
                            v-model="newAbsence.startDate"
                            type="date"
                            class="w-full border rounded-lg px-3 py-2"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input
                            v-model="newAbsence.endDate"
                            type="date"
                            class="w-full border rounded-lg px-3 py-2"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Reason (Optional)</label>
                        <input
                            v-model="newAbsence.reason"
                            type="text"
                            placeholder="e.g., Vacation, Conference, etc."
                            class="w-full border rounded-lg px-3 py-2"
                        />
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button
                        @click="showAbsenceModal = false"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                        Cancel
                    </button>
                    <button
                        @click="addAbsence"
                        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
                    >
                        Add Time Off
                    </button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
